#!/bin/bash

# Build script for SSH plugin bindings

set -e

echo "Building SSH plugin for multiple platforms..."

# Create output directories
mkdir -p bindings/android/jniLibs/arm64-v8a
mkdir -p bindings/android/jniLibs/x86_64
mkdir -p bindings/ios

# 检查是否安装了 Rust 和 Cargo
if ! command -v rustup &> /dev/null; then
    echo "Error: rustup not found. Please install Rust from https://rustup.rs/"
    exit 1
fi

if ! command -v cargo &> /dev/null; then
    echo "Error: cargo not found. Please install Rust from https://rustup.rs/"
    exit 1
fi

# 检查是否安装了 Android NDK
if [ -z "$ANDROID_NDK_HOME" ]; then
    echo "Warning: ANDROID_NDK_HOME environment variable not set."
    echo "Android builds may fail. Please set ANDROID_NDK_HOME to your Android NDK path."
fi

# 检查是否安装了 cargo-ndk
if ! command -v cargo-ndk &> /dev/null; then
    echo "Installing cargo-ndk..."
    cargo install cargo-ndk
fi

# 构建本地版本（用于测试）
echo "Building for host platform..."
cargo build --release

# 构建 Android 版本
if [ -n "$ANDROID_NDK_HOME" ]; then
    echo "Building for Android ARM64..."
    rustup target add aarch64-linux-android
    cargo ndk --target aarch64-linux-android --platform 21 -- build --release
    cp target/aarch64-linux-android/release/libssh_plugin.so bindings/android/jniLibs/arm64-v8a/

    echo "Building for Android x86_64..."
    rustup target add x86_64-linux-android
    cargo ndk --target x86_64-linux-android --platform 21 -- build --release
    cp target/x86_64-linux-android/release/libssh_plugin.so bindings/android/jniLibs/x86_64/
else
    echo "Skipping Android builds due to missing NDK."
fi

# 构建 iOS 版本
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Building for iOS ARM64..."
    rustup target add aarch64-apple-ios
    cargo build --target aarch64-apple-ios --release

    echo "Building for iOS Simulator..."
    rustup target add x86_64-apple-ios
    cargo build --target x86_64-apple-ios --release

    # 检查生成的文件
    echo "Checking generated files..."
    ls -la target/aarch64-apple-ios/release/libssh_plugin.*
    ls -la target/x86_64-apple-ios/release/libssh_plugin.*

    # 创建 XCFramework
    echo "Creating XCFramework..."
    mkdir -p bindings/ios/build/aarch64
    mkdir -p bindings/ios/build/x86_64

    # 检查文件是否存在并复制
    if [ -f "target/aarch64-apple-ios/release/libssh_plugin.a" ]; then
        cp target/aarch64-apple-ios/release/libssh_plugin.a bindings/ios/build/aarch64/
        echo "Copied ARM64 static library"
    else
        echo "Warning: ARM64 static library not found"
    fi

    if [ -f "target/x86_64-apple-ios/release/libssh_plugin.a" ]; then
        cp target/x86_64-apple-ios/release/libssh_plugin.a bindings/ios/build/x86_64/
        echo "Copied x86_64 static library"
    else
        echo "Warning: x86_64 static library not found"
    fi

    # 如果两个静态库都存在，创建 XCFramework
    if [ -f "bindings/ios/build/aarch64/libssh_plugin.a" ] && [ -f "bindings/ios/build/x86_64/libssh_plugin.a" ]; then
        if command -v xcodebuild &> /dev/null; then
            rm -rf bindings/ios/SSHPlugin.xcframework
            xcodebuild -create-xcframework \
                -library bindings/ios/build/aarch64/libssh_plugin.a \
                -library bindings/ios/build/x86_64/libssh_plugin.a \
                -output bindings/ios/SSHPlugin.xcframework
            echo "XCFramework created successfully"
        else
            echo "Warning: xcodebuild not found. XCFramework creation skipped."
        fi
    else
        echo "Warning: Cannot create XCFramework - missing static libraries"
    fi
else
    echo "Skipping iOS builds (only available on macOS)."
fi

echo "Build completed. Bindings are available in the bindings/ directory."

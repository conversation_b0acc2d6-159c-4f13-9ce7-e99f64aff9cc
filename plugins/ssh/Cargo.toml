[package]
name = "ssh-plugin"
version = "0.1.0"
edition = "2024"

[lib]
crate-type = ["cdylib", "staticlib"]
name = "ssh_plugin"

[dependencies]
uniffi = "0.29"
ssh-rs = "0.3"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
anyhow = "1.0"
base64 = "0.21"

[build-dependencies]
uniffi = { version = "0.29", features = ["build"] }

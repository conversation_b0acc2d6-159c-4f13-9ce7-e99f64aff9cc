/Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/deps/libssh_plugin.dylib: src/lib.rs /Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml /Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/build/ssh-plugin-8c9419d187badfeb/out/ssh.uniffi.rs

/Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/deps/libssh_plugin.a: src/lib.rs /Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml /Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/build/ssh-plugin-8c9419d187badfeb/out/ssh.uniffi.rs

/Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/deps/ssh_plugin.d: src/lib.rs /Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml /Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/build/ssh-plugin-8c9419d187badfeb/out/ssh.uniffi.rs

src/lib.rs:
/Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml:
/Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/build/ssh-plugin-8c9419d187badfeb/out/ssh.uniffi.rs:

# env-dep:OUT_DIR=/Users/<USER>/projects/remote-coding/plugins/ssh/target/aarch64-apple-ios/release/build/ssh-plugin-8c9419d187badfeb/out

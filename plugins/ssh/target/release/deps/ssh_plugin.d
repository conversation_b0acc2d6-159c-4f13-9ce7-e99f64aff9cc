/Users/<USER>/projects/remote-coding/plugins/ssh/target/release/deps/libssh_plugin.dylib: src/lib.rs /Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml /Users/<USER>/projects/remote-coding/plugins/ssh/target/release/build/ssh-plugin-45a18819f35e1c78/out/ssh.uniffi.rs

/Users/<USER>/projects/remote-coding/plugins/ssh/target/release/deps/libssh_plugin.a: src/lib.rs /Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml /Users/<USER>/projects/remote-coding/plugins/ssh/target/release/build/ssh-plugin-45a18819f35e1c78/out/ssh.uniffi.rs

/Users/<USER>/projects/remote-coding/plugins/ssh/target/release/deps/ssh_plugin.d: src/lib.rs /Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml /Users/<USER>/projects/remote-coding/plugins/ssh/target/release/build/ssh-plugin-45a18819f35e1c78/out/ssh.uniffi.rs

src/lib.rs:
/Users/<USER>/projects/remote-coding/plugins/ssh/Cargo.toml:
/Users/<USER>/projects/remote-coding/plugins/ssh/target/release/build/ssh-plugin-45a18819f35e1c78/out/ssh.uniffi.rs:

# env-dep:OUT_DIR=/Users/<USER>/projects/remote-coding/plugins/ssh/target/release/build/ssh-plugin-45a18819f35e1c78/out

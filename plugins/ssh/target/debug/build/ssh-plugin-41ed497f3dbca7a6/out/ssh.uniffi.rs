// This file was autogenerated by some hot garbage in the `uniffi` crate.
// Trust me, you don't want to mess with it!


::uniffi::setup_scaffolding!("ssh_plugin");

// Export info about this UDL file
// See `uniffi_bindgen::macro_metadata` for how this is used.

const UNIFFI_META_CONST_UDL_SSH_PLUGIN: ::uniffi::MetadataBuffer = ::uniffi::MetadataBuffer::from_code(::uniffi::metadata::codes::UDL_FILE)
    .concat_str("ssh_plugin")
    .concat_str("ssh_plugin")
    .concat_str("ssh");

#[doc(hidden)]
#[unsafe(no_mangle)]
pub static UNIFFI_META_UDL_SSH_PLUGIN: [u8; UNIFFI_META_CONST_UDL_SSH_PLUGIN.size] = UNIFFI_META_CONST_UDL_SSH_PLUGIN.into_array();

















// Enum definitions, corresponding to `enum` in UDL.

#[::uniffi::udl_derive(Enum)]
enum r#ConnectionStatus {
    r#Disconnected {
    },
    r#Connecting {
    },
    r#Connected {
    },
    r#Error {
    },
}



// Error definitions, corresponding to `error` in the UDL.

#[::uniffi::udl_derive(Error)]
#[uniffi(flat_error)]

enum r#SshError {
    r#ConnectionFailed {
    },
    r#AuthenticationFailed {
    },
    r#CommandExecutionFailed {
    },
    r#FileTransferFailed {
    },
    r#InvalidConfig {
    },
    r#NotConnected {
    },
}



// Record definitions, implemented as method-less structs, corresponding to `dictionary` objects.


#[::uniffi::udl_derive(Record)]
struct r#FileSystemItem {
    r#name: ::std::string::String,
    r#path: ::std::string::String,
    r#item_type: ::std::string::String,
    r#size: ::std::option::Option<u64>,
    r#permissions: ::std::option::Option<::std::string::String>,
    r#modified_time: ::std::option::Option<u64>,
}


#[::uniffi::udl_derive(Record)]
struct r#SshConfig {
    r#host: ::std::string::String,
    r#port: u16,
    r#username: ::std::string::String,
    r#password: ::std::option::Option<::std::string::String>,
    r#private_key: ::std::option::Option<::std::string::String>,
    r#passphrase: ::std::option::Option<::std::string::String>,
}


// Top level functions, corresponding to UDL `namespace` functions.

#[::uniffi::export_for_udl]
pub fn r#connect_ssh(
    r#config: r#SshConfig,
) -> ::std::result::Result::<::std::sync::Arc<r#SshConnection>, r#SshError>
{
    unreachable!()
}
// Object definitions, corresponding to UDL `interface` definitions.


#[::uniffi::udl_derive(Object)]
struct r#SshConnection { }
#[::uniffi::export_for_udl]
impl r#SshConnection {
    #[uniffi::constructor]
    pub fn r#new(
        r#config: r#SshConfig,
    ) -> ::std::sync::Arc<r#SshConnection>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#connect(
        &self,
    ) -> ::std::result::Result::<(), r#SshError>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#disconnect(
        &self,
    )
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#download_file(
        &self,
        r#remote_path: ::std::string::String,
        r#local_path: ::std::string::String,
    ) -> ::std::result::Result::<(), r#SshError>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#execute_command(
        &self,
        r#command: ::std::string::String,
    ) -> ::std::result::Result::<::std::string::String, r#SshError>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#get_config(
        &self,
    ) -> r#SshConfig
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#get_status(
        &self,
    ) -> r#ConnectionStatus
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#is_connected(
        &self,
    ) -> bool
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#list_directory(
        &self,
        r#path: ::std::string::String,
    ) -> ::std::result::Result::<std::vec::Vec<r#FileSystemItem>, r#SshError>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#read_file(
        &self,
        r#file_path: ::std::string::String,
    ) -> ::std::result::Result::<::std::string::String, r#SshError>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#upload_file(
        &self,
        r#local_path: ::std::string::String,
        r#remote_path: ::std::string::String,
    ) -> ::std::result::Result::<(), r#SshError>
    {
        unreachable!()
    }
}
#[::uniffi::export_for_udl]
impl r#SshConnection {
    pub fn r#write_file(
        &self,
        r#file_path: ::std::string::String,
        r#content: ::std::string::String,
    ) -> ::std::result::Result::<(), r#SshError>
    {
        unreachable!()
    }
}




// Callback Interface definitions, corresponding to UDL `callback interface` definitions.


// Export scaffolding checksums for UDL items

#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_func_connect_ssh() -> u16 {
    2968
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_connect() -> u16 {
    17801
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_disconnect() -> u16 {
    13122
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_download_file() -> u16 {
    31955
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_execute_command() -> u16 {
    42505
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_get_config() -> u16 {
    16141
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_get_status() -> u16 {
    60461
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_is_connected() -> u16 {
    57869
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_list_directory() -> u16 {
    49472
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_read_file() -> u16 {
    49891
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_upload_file() -> u16 {
    60895
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_method_sshconnection_write_file() -> u16 {
    62633
}
#[unsafe(no_mangle)]
#[doc(hidden)]
pub extern "C" fn r#uniffi_ssh_plugin_checksum_constructor_sshconnection_new() -> u16 {
    5274
}
{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"ecdsa\", \"ed25519\", \"rand_core\", \"rsa\", \"std\"]", "declared_features": "[\"alloc\", \"crypto\", \"default\", \"dsa\", \"ecdsa\", \"ed25519\", \"encryption\", \"getrandom\", \"p256\", \"p384\", \"p521\", \"rand_core\", \"rsa\", \"serde\", \"std\", \"tdes\"]", "target": 8263524556018716454, "profile": 8276155916380437441, "path": 10224942706694856807, "deps": [[1290802279132420970, "rsa", false, 13840813345525131601], [5218994449591892524, "sec1", false, 15365033707600578735], [5306016253860807931, "ed25519_dalek", false, 7251282454127528195], [6528079939221783635, "zeroize", false, 17832751012306876232], [9857275760291862238, "sha2", false, 3781757779481291899], [9865170134632611639, "encoding", false, 8504327345272733139], [12154240879505460262, "bigint", false, 13632025843462391966], [13895928991373641935, "signature", false, 14773357031319659593], [15628078057051191073, "cipher", false, 3831329920269467162], [17003143334332120809, "subtle", false, 13930322706083000368], [18130209639506977569, "rand_core", false, 3297861698997103167]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ssh-key-1184e23f78f4f4c8/dep-lib-ssh_key", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
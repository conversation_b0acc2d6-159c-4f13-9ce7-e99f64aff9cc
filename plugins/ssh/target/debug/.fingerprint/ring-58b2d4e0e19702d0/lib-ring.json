{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17591616432441575691, "profile": 8276155916380437441, "path": 246531356198063654, "deps": [[2317793503723491507, "untrusted", false, 12501213915819568649], [3016319839805820069, "build_script_build", false, 8500582161042769022]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ring-58b2d4e0e19702d0/dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
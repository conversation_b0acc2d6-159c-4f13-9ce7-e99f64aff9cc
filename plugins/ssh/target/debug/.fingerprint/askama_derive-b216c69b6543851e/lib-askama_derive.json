{"rustc": 5357548097637079788, "features": "[\"alloc\", \"config\"]", "declared_features": "[\"alloc\", \"blocks\", \"code-in-doc\", \"config\", \"default\", \"derive\", \"full\", \"serde_json\", \"std\", \"urlencode\"]", "target": 4268644018954439499, "profile": 5619514355126206069, "path": 14345075790727967471, "deps": [[3060637413840920116, "proc_macro2", false, 16204403690456385610], [4974441333307933176, "syn", false, 1064456320225054249], [5268357711175466720, "parser", false, 1910685265091436037], [9083217064824284637, "basic_toml", false, 9776523513256926270], [9689903380558560274, "serde", false, 12035084409999642576], [15932120279885307830, "memchr", false, 8457407745066394772], [16257276029081467297, "serde_derive", false, 14795543397203868863], [17990358020177143287, "quote", false, 7934055633737951708], [18335655851112826545, "rustc_hash", false, 1696578474437101320]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/askama_derive-b216c69b6543851e/dep-lib-askama_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
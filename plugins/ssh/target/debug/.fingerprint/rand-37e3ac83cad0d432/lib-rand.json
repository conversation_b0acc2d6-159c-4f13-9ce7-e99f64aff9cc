{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 8276155916380437441, "path": 8952531468049543929, "deps": [[1573238666360410412, "rand_chacha", false, 1733285222353581384], [4684437522915235464, "libc", false, 14888376552997639404], [18130209639506977569, "rand_core", false, 3297861698997103167]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-37e3ac83cad0d432/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
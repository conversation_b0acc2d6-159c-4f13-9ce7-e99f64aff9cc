{"rustc": 5357548097637079788, "features": "[\"cargo-metadata\", \"default\"]", "declared_features": "[\"bindgen\", \"bindgen-tests\", \"build\", \"cargo-metadata\", \"cli\", \"default\", \"ffi-trace\", \"scaffolding-ffi-buffer-fns\", \"tokio\", \"wasm-unstable-single-threaded\"]", "target": 11357443623122967367, "profile": 8276155916380437441, "path": 7673726115795394469, "deps": [[1934862791020766283, "uniffi_core", false, 14630783953730784775], [4993739160566741368, "uniffi_macros", false, 16429267937130657345], [11655476559277113544, "cargo_metadata", false, 14619819624899836473], [13625485746686963219, "anyhow", false, 16836653962153198102], [15291410421973896058, "uniffi_pipeline", false, 7757616642331238602]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/uniffi-192f41209043fee6/dep-lib-uniffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
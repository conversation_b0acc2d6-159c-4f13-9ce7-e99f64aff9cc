{"rustc": 5357548097637079788, "features": "[\"build\", \"cargo-metadata\", \"default\"]", "declared_features": "[\"bindgen\", \"bindgen-tests\", \"build\", \"cargo-metadata\", \"cli\", \"default\", \"ffi-trace\", \"scaffolding-ffi-buffer-fns\", \"tokio\", \"wasm-unstable-single-threaded\"]", "target": 11357443623122967367, "profile": 3033921117576893, "path": 7673726115795394469, "deps": [[1934862791020766283, "uniffi_core", false, 14103042175176871792], [4993739160566741368, "uniffi_macros", false, 16429267937130657345], [11655476559277113544, "cargo_metadata", false, 13071250231246556952], [13625485746686963219, "anyhow", false, 3848219591020023869], [15291410421973896058, "uniffi_pipeline", false, 16159355713200106395], [17376649028097042447, "uniffi_build", false, 4376225568501543091]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/uniffi-b36098d8dd2dcae0/dep-lib-uniffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
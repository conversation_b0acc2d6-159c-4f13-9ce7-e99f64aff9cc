{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2734000389832276527, "path": 334462364753712715, "deps": [[4684437522915235464, "libc", false, 14888376552997639404], [7896293946984509699, "bitflags", false, 14736925607002260825], [8253628577145923712, "libc_errno", false, 7561930716189634696], [12053020504183902936, "build_script_build", false, 1488757242886393995]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-5ec0a7f85c2f7690/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
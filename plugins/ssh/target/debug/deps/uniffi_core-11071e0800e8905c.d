/Users/<USER>/projects/remote-coding/plugins/ssh/target/debug/deps/libuniffi_core-11071e0800e8905c.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/callbackinterface.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/ffidefault.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreignbytes.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreigncallbacks.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreignfuture.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/handle.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustbuffer.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustcalls.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/future.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/scheduler.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi_converter_impls.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi_converter_traits.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/metadata.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/oneshot.rs

/Users/<USER>/projects/remote-coding/plugins/ssh/target/debug/deps/uniffi_core-11071e0800e8905c.d: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/callbackinterface.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/ffidefault.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreignbytes.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreigncallbacks.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreignfuture.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/handle.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustbuffer.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustcalls.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/future.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/scheduler.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi_converter_impls.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi_converter_traits.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/metadata.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/oneshot.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/callbackinterface.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/ffidefault.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreignbytes.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreigncallbacks.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/foreignfuture.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/handle.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustbuffer.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustcalls.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/future.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi/rustfuture/scheduler.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi_converter_impls.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/ffi_converter_traits.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/metadata.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/uniffi_core-0.29.3/src/oneshot.rs:

# env-dep:CARGO_PKG_VERSION=0.29.3
